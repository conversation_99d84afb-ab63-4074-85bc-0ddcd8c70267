const Leader = require("../models/leader");
const Department = require("../models/department");
const Position = require("../models/position");

// Get all departments (from both Department model and leaders)
const getAllDepartments = async (req, res) => {
  try {
    // Get departments from Department model, sorted by level (lowest first)
    const storedDepartments = await Department.find({}).sort({ level: 1, createdAt: 1 });

    // Get unique departments from leaders (for backward compatibility)
    // Since leaders now use ObjectId references, we need to populate the department data
    const leadersWithDepartments = await Leader.find({}).populate('department', 'name level').select('department');

    // Combine and deduplicate departments
    const allDepartmentNames = new Set();
    const departmentMap = new Map(); // Store department data including level

    // Add stored departments
    storedDepartments.forEach(dept => {
      allDepartmentNames.add(dept.name);
      departmentMap.set(dept.name, {
        _id: dept._id.toString(),
        name: dept.name,
        level: dept.level || 5 // Default level if not set
      });
    });

    // Add leader departments (for any that might not be in Department model)
    leadersWithDepartments.forEach(leader => {
      if (leader.department) {
        let deptName = null;
        let deptLevel = 5; // Default level

        // Handle both string (old format) and object (new format) departments
        if (typeof leader.department === 'string') {
          deptName = leader.department.trim();
        } else if (leader.department && typeof leader.department === 'object' && leader.department.name) {
          deptName = leader.department.name;
          deptLevel = leader.department.level || 5;
        }

        if (deptName && typeof deptName === 'string' && !departmentMap.has(deptName)) {
          allDepartmentNames.add(deptName);
          departmentMap.set(deptName, {
            _id: `temp_${departmentMap.size}`,
            name: deptName,
            level: deptLevel
          });
        }
      }
    });

    // Create final department list sorted by level (lowest first)
    const departmentList = Array.from(departmentMap.values()).sort((a, b) => {
      if (a.level !== b.level) {
        return a.level - b.level; // Sort by level ascending (lowest first)
      }
      return a.name.localeCompare(b.name); // Then by name alphabetically
    });

    res.status(200).json({
      success: true,
      data: departmentList,
      message: "Lấy danh sách phòng ban thành công"
    });
  } catch (error) {
    console.error("Error in getAllDepartments:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi lấy danh sách phòng ban"
    });
  }
};

// Create new department (store in Department model)
const createDepartment = async (req, res) => {
  try {
    const { name } = req.body;

    if (!name || !name.trim()) {
      return res.status(400).json({
        success: false,
        message: "Tên phòng ban không được để trống"
      });
    }

    // Check if department already exists in Department model
    const existingDepartment = await Department.findOne({ name: name.trim() });
    if (existingDepartment) {
      return res.status(409).json({
        success: false,
        message: "Phòng ban đã tồn tại"
      });
    }

    // Check if department exists in leaders (for backward compatibility)
    // Only check string department fields to avoid casting errors with ObjectId fields
    let existingInLeaders = null;
    try {
      existingInLeaders = await Leader.findOne({
        department: { $type: "string", $eq: name.trim() }
      });
    } catch (error) {
      // Ignore errors from ObjectId casting - this is expected for newer records
      console.log("Checking leaders with string department fields (backward compatibility)");
    }

    if (existingInLeaders) {
      return res.status(409).json({
        success: false,
        message: "Phòng ban đã tồn tại"
      });
    }

    // Create new department
    const newDepartment = new Department({
      name: name.trim()
    });

    const savedDepartment = await newDepartment.save();

    // Create default positions for the new department
    try {
      await Position.createDefaultPositionsForDepartment(savedDepartment._id);
    } catch (positionError) {
      console.error("Error creating default positions:", positionError);
      // Don't fail the department creation if position creation fails
    }

    res.status(201).json({
      success: true,
      data: {
        id: savedDepartment._id.toString(),
        name: savedDepartment.name
      },
      message: "Phòng ban được tạo thành công"
    });
  } catch (error) {
    console.error("Error in createDepartment:", error);
    if (error.code === 11000) {
      return res.status(409).json({
        success: false,
        message: "Phòng ban đã tồn tại"
      });
    }
    res.status(500).json({
      success: false,
      message: "Lỗi server khi tạo phòng ban"
    });
  }
};

// Update department name (update both Department model and all leaders)
const updateDepartment = async (req, res) => {
  try {
    const { id } = req.params;
    const { name } = req.body;

    console.log("Backend - updateDepartment called:", { id, name });

    if (!name || !name.trim()) {
      console.log("Backend - Empty name provided");
      return res.status(400).json({
        success: false,
        message: "Tên phòng ban không được để trống"
      });
    }

    // Try to find department in Department model first
    console.log("Backend - Looking for department by ID:", id);
    const existingDepartment = await Department.findById(id);
    console.log("Backend - Found department:", existingDepartment);
    let oldName = null;

    if (existingDepartment) {
      oldName = existingDepartment.name;
      console.log("Backend - Using existing department name:", oldName);
    } else {
      console.log("Backend - Department not found by ID, trying fallback");
      // Fallback: try to find by index in leaders (for backward compatibility)
      const departments = await Leader.distinct('department');
      console.log("Backend - Available departments from leaders:", departments);
      oldName = departments[parseInt(id)] || departments.find(d => d && d.includes(name));
      console.log("Backend - Fallback oldName:", oldName);
    }

    if (!oldName) {
      console.log("Backend - No department found with ID:", id);
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy phòng ban"
      });
    }

    // Check if new name already exists (and different from old name)
    if (oldName !== name.trim()) {
      console.log("Backend - Checking for duplicate name:", name.trim());
      const existingByName = await Department.findOne({ name: name.trim() });
      console.log("Backend - Existing department by name:", existingByName);

      // For leaders, we need to be careful about ObjectId vs string fields
      // Only check string department fields to avoid casting errors
      let existingInLeaders = null;
      try {
        existingInLeaders = await Leader.findOne({
          department: { $type: "string", $eq: name.trim() }
        });
        console.log("Backend - Existing leader with string department:", existingInLeaders);
      } catch (error) {
        console.log("Backend - Error checking leaders (expected for ObjectId fields):", error.message);
      }

      if (existingByName || existingInLeaders) {
        return res.status(409).json({
          success: false,
          message: "Tên phòng ban đã tồn tại"
        });
      }
    }

    // Update department in Department model if it exists
    if (existingDepartment) {
      existingDepartment.name = name.trim();
      await existingDepartment.save();

      // Leaders that reference this department by ObjectId don't need updating
      // since they reference the department by ID, not by name
      console.log("Backend - Department updated in Department model, ObjectId references remain valid");
    }

    // Only update leaders that still use string department names (for backward compatibility)
    // We need to be careful to only update string fields, not ObjectId fields
    console.log("Backend - Updating leaders with string department name:", oldName);
    try {
      const stringUpdateResult = await Leader.updateMany(
        { 
          department: { $type: "string", $eq: oldName }
        },
        { 
          $set: { department: name.trim() }
        }
      );
      console.log("Backend - String update result:", stringUpdateResult);
    } catch (stringUpdateError) {
      console.log("Backend - Error updating string departments (this is expected if all departments are ObjectIds):", stringUpdateError.message);
    }

    res.status(200).json({
      success: true,
      data: { id, name: name.trim() },
      message: "Cập nhật phòng ban thành công"
    });
  } catch (error) {
    console.error("Error in updateDepartment:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi cập nhật phòng ban"
    });
  }
};

// Delete department (only if no leaders belong to it)
const deleteDepartment = async (req, res) => {
  try {
    const { id } = req.params;

    console.log("Backend - deleteDepartment called with ID:", id);

    // Try to find department in Department model first
    const existingDepartment = await Department.findById(id);
    let departmentName = null;

    if (existingDepartment) {
      departmentName = existingDepartment.name;
      console.log("Backend - Found department:", departmentName);
    } else {
      console.log("Backend - Department not found in Department model");
      return res.status(404).json({
        success: false,
        message: "Không tìm thấy phòng ban"
      });
    }

    // Check if any leaders belong to this department
    // Check both ObjectId references and string references (for backward compatibility)
    let leadersCount = 0;

    try {
      // Count leaders with ObjectId reference to this department (only if id is valid ObjectId)
      let objectIdCount = 0;
      if (id.match(/^[0-9a-fA-F]{24}$/)) {
        objectIdCount = await Leader.countDocuments({ department: id });
        console.log("Backend - Leaders with ObjectId reference:", objectIdCount);
      } else {
        console.log("Backend - Invalid ObjectId format, skipping ObjectId query");
      }

      // Count leaders with string reference to this department name
      const stringCount = await Leader.countDocuments({
        department: { $type: "string", $eq: departmentName }
      });
      console.log("Backend - Leaders with string reference:", stringCount);

      leadersCount = objectIdCount + stringCount;
      console.log("Backend - Total leaders count:", leadersCount);
    } catch (countError) {
      console.error("Backend - Error counting leaders:", countError);
      // If there's an error counting, assume there might be leaders and prevent deletion
      return res.status(500).json({
        success: false,
        message: "Lỗi khi kiểm tra lãnh đạo thuộc phòng ban"
      });
    }

    if (leadersCount > 0) {
      return res.status(400).json({
        success: false,
        message: `Không thể xóa! Còn ${leadersCount} lãnh đạo thuộc phòng ban này`
      });
    }

    // Check if any positions belong to this department
    try {
      let positionsCount = 0;
      if (id.match(/^[0-9a-fA-F]{24}$/)) {
        positionsCount = await Position.countDocuments({ department: id });
        console.log("Backend - Positions count:", positionsCount);

        if (positionsCount > 0) {
          // Delete all positions for this department
          await Position.deleteMany({ department: id });
          console.log("Backend - Deleted positions for department");
        }
      } else {
        console.log("Backend - Invalid ObjectId format, skipping position cleanup");
      }
    } catch (positionError) {
      console.error("Backend - Error handling positions:", positionError);
      // Continue with department deletion even if position cleanup fails
    }

    // Delete from Department model
    await Department.findByIdAndDelete(id);
    console.log("Backend - Department deleted successfully");

    res.status(200).json({
      success: true,
      message: "Xóa phòng ban thành công"
    });
  } catch (error) {
    console.error("Error in deleteDepartment:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi xóa phòng ban"
    });
  }
};

// Reorder departments by updating their level values
const reorderDepartments = async (req, res) => {
  try {
    const { departmentOrder } = req.body;

    if (!Array.isArray(departmentOrder)) {
      return res.status(400).json({
        success: false,
        message: "departmentOrder phải là một mảng"
      });
    }

    // Validate that all items have required fields
    for (let i = 0; i < departmentOrder.length; i++) {
      const item = departmentOrder[i];
      if (!item._id || typeof item.level !== 'number') {
        return res.status(400).json({
          success: false,
          message: `Phần tử thứ ${i + 1} thiếu _id hoặc level`
        });
      }
    }

    // Update departments in bulk
    const updatePromises = departmentOrder.map(item =>
      Department.findByIdAndUpdate(
        item._id,
        { level: item.level },
        { new: true }
      )
    );

    const updatedDepartments = await Promise.all(updatePromises);

    // Check if any updates failed
    const failedUpdates = updatedDepartments.filter(dept => !dept);
    if (failedUpdates.length > 0) {
      return res.status(400).json({
        success: false,
        message: "Một số phòng ban không thể cập nhật"
      });
    }

    res.status(200).json({
      success: true,
      data: updatedDepartments,
      message: "Cập nhật thứ tự phòng ban thành công"
    });
  } catch (error) {
    console.error("Error in reorderDepartments:", error);
    res.status(500).json({
      success: false,
      message: "Lỗi server khi cập nhật thứ tự phòng ban"
    });
  }
};

module.exports = {
  getAllDepartments,
  createDepartment,
  updateDepartment,
  deleteDepartment,
  reorderDepartments
};
